package com.sgmw.ksongs.ui.singerlist


import android.view.View
import androidx.appcompat.widget.TooltipCompat
import androidx.navigation.fragment.findNavController
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.ToastUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener

import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentHotSingerListBinding
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.SearchResultBean

import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.adapter.SingerInfoAdapter
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.viewmodel.singerlist.HotSingerListViewModel
import com.sgmw.ksongs.widget.AccessibilityGridLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration

class HotSingerListFragment(filterSingerArea: Int,singerArea:String) :
    BaseFrameFragment<FragmentHotSingerListBinding, HotSingerListViewModel>() {

    private val TAG = HotSingerListFragment::class.java.simpleName
    private val SPAN_COUNT = 7
    private val mSecondTitles = arrayOf("全部", "男", "女", "组合")
    private var mFilterSingerArea: Int = 100
    private var mFilterSingerType: Int = 100
    private var mSingerArea = singerArea
    private var mSingerType = "全部"

    init {
        Log.d(TAG, "mFilterSingerArea = $mFilterSingerArea")
        mFilterSingerArea = filterSingerArea
    }

    private val mSingerListAdapter: SingerInfoAdapter by lazy {
        SingerInfoAdapter()
    }

    override fun FragmentHotSingerListBinding.initView() {
        Log.d(TAG, "initView")
        mBinding?.let { binding ->
            mSecondTitles.map {
                val tab = binding.tabLayout.newTab()
                tab.text = it
                binding.tabLayout.addTab(tab)
            }
            binding.tabLayout.getTabAt(0)?.select()
            binding.tabLayout.addOnTabSelectedListener(object : OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    tab?.position.let {
                        when (it) {
                            1, 2, 3 -> mFilterSingerType = it - 1
                            0 -> mFilterSingerType = 100
                        }
                    }
                    mSingerType = tab?.text.toString()
                    val cardName = getString(R.string.home_title_hot_singer) + SPLIT+ mSingerArea +SPLIT + mSingerType
                    SensorsDataManager.trackSongStationEvent(cardName)

                    binding.stateLayout.showLoading()
                    mViewModel?.getSingerList(mFilterSingerArea, mFilterSingerType, Operation.NewData)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {

                }

                override fun onTabReselected(tab: TabLayout.Tab?) {

                }

            })

            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                tab?.let {
                    TooltipCompat.setTooltipText(it.view, null)
                }
            }
            val layoutManagerSinger =
                AccessibilityGridLayoutManager(context, SPAN_COUNT)
            binding.recyclerview.layoutManager = layoutManagerSinger
            binding.recyclerview.adapter = mSingerListAdapter
            binding.recyclerview.addItemDecoration(
                SpaceItemDecoration(
                    bottomSpace = SizeUtils.dp2px(24F)
                )
            )
            mSingerListAdapter.setOnItemClickListener { adapter, v, position ->
                val singer = mSingerListAdapter.data[position]
                val cardName = getString(R.string.home_title_hot_singer) + SPLIT+ mSingerArea +SPLIT + mSingerType + SPLIT + singer.singer_name
                NavigationUtils.navigateSafely(
                    findNavController(),
                    R.id.action_hot_singer_list_to_song_list_by_singer,
                    SongListBySingerFragment.createBundle(
                        singer.singer_id,
                        singer.singer_name,
                        cardName
                    )
                )
            }

            binding.refreshLayout.setOnLoadMoreListener {
                Log.d(TAG, "加载更多数据")
                mViewModel?.getSingerList(mFilterSingerArea, mFilterSingerType, Operation.LoadMore)
            }


        }
    }

    private fun setErrorEntryListener() {
        mBinding?.stateLayout?.setErrorRetryClickListener {
            mBinding?.stateLayout?.showLoading()
            mViewModel?.getSingerList(mFilterSingerArea, mFilterSingerType, Operation.NewData)
        }
    }

    /**
     * 设置空数据监听
     */
    private fun setEmptyDataListener() {
        mBinding?.stateLayout?.setEmptyClickListener {
            NavigationUtils.navigateSafely(findNavController(), R.id.action_hot_singer_list_to_search)
        }
    }

    override fun needSkinApply()  = true
    override fun initRequestData() {
        Log.d(TAG, "initRequestData")
        mViewModel?.let {
            mBinding?.stateLayout?.showLoading()
            it.getSingerList(mFilterSingerArea, mFilterSingerType, Operation.NewData)
        }
    }

    override fun initObserve() {
        mBinding?.let { binding ->
            mViewModel?.let { viewModel ->
                viewModel.mSingerListBean.observe(viewLifecycleOwner) {
                    it.onSuccess { value, operation ->
                        if (value == null) {
                            binding.stateLayout.showEmpty()
                            setEmptyDataListener()
                        } else {
                            showSingerListView(binding, value, operation)
                        }
                    }.onFailure { resultCode, operation ->
                        when (operation) {
                            Operation.NewData -> {
                                binding.stateLayout?.showError()
                                setErrorEntryListener()
                            }

                            Operation.LoadMore -> {
                                binding.refreshLayout.finishLoadMore(false)
                                showToast(R.string.load_more_failed)
                            }

                            else -> {}
                        }
                    }
                }
            }
        }

    }


    private fun showSingerListView(
        binding: FragmentHotSingerListBinding,
        searchResultBean: SearchResultBean,
        operation: Operation
    ) {

        val singers = searchResultBean.singers
        Log.d(TAG, "showSingerListView $operation $singers")
        when (operation) {
            Operation.LoadMore -> {
                if (singers.isNullOrEmpty()) {
                    if (!searchResultBean.has_more) {
                        binding.refreshLayout.finishLoadMoreWithNoMoreData()
                        // 没有更多数据时，禁用 hasMoreData 标志，允许向上滑动回弹效果
                        binding.recyclerview.setHasMoreData(false)
                        Log.d(TAG, "LoadMore: 设置没有更多数据")
                    }
                } else {
                    mSingerListAdapter.addData(singers)
                    // 根据是否还有更多数据来决定SmartRefreshLayout的状态
                    if (searchResultBean.has_more) {
                        binding.refreshLayout.finishLoadMore()
                        // 还有更多数据，设置 hasMoreData 为 true，不启用向上滑动回弹
                        binding.recyclerview.setHasMoreData(true)
                        binding.refreshLayout.setNoMoreData(false)
                        Log.d(TAG, "LoadMore: 设置还有更多数据")
                    } else {
                        binding.refreshLayout.finishLoadMoreWithNoMoreData()
                        // 没有更多数据时，启用向上滑动回弹效果
                        binding.recyclerview.setHasMoreData(false)
                        binding.refreshLayout.setNoMoreData(true)
                        Log.d(TAG, "LoadMore: 设置没有更多数据")
                    }
                }
            }

            Operation.NewData -> {
                if (singers.isNullOrEmpty()) {
                    binding.stateLayout.showEmpty()
                    binding.recyclerview.setHasMoreData(false)
                    Log.d(TAG, "NewData: 数据为空，设置没有更多数据")
                } else {
                    if (binding.recyclerview.adapter == null) {
                        mSingerListAdapter.setNewInstance(singers)
                    } else {
                        binding.recyclerview.scrollToPosition(0)
                        mSingerListAdapter.setList(singers)
                    }
                    binding.stateLayout.showContent()
                    // 设置是否有更多数据
                    binding.recyclerview.setHasMoreData(searchResultBean.has_more)
                    // 如果没有更多数据，需要设置 SmartRefreshLayout 的状态
                    if (!searchResultBean.has_more) {
                        binding.refreshLayout.setNoMoreData(true)
                        Log.d(TAG, "NewData: 设置 SmartRefreshLayout 没有更多数据")
                    } else {
                        binding.refreshLayout.setNoMoreData(false)
                        Log.d(TAG, "NewData: 设置 SmartRefreshLayout 还有更多数据")
                    }
                }

            }

            else -> {}
        }


    }
}