package com.sgmw.ksongs.ui.category

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentCategorySongListBinding
import com.sgmw.ksongs.model.bean.CategoryThemeBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager

/**
 * 分类点歌二级列表页面 展示具体分类歌曲
 * 独立实现，不再依赖 HotTopicListFragment
 */
class CategorySongListFragment : BaseFrameFragment<FragmentCategorySongListBinding, CategorySongListViewModel>() {

    companion object {
        private const val CATEGORY_THEME_BEAN = "category_theme_bean"
        private const val CATEGORY_CARD_NAME = "card_name"

        fun createBundle(categoryBean: CategoryThemeBean, cardName: String = ""): Bundle {
            return Bundle().apply {
                putParcelable(CATEGORY_THEME_BEAN, categoryBean)
                putString(CATEGORY_CARD_NAME, cardName)
            }
        }
    }

    override fun needSkinApply() = true

    private val adapter = RankListAdapter()
    private var cardName = ""

    override fun FragmentCategorySongListBinding.initView() {
        val categoryThemeBean = arguments?.getParcelable<CategoryThemeBean>(CATEGORY_THEME_BEAN)
        cardName = arguments?.getString(CATEGORY_CARD_NAME) ?: ""

        // 设置标题和返回按钮
        tvTitle.text = categoryThemeBean?.theme_name ?: ""
        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }

        // 初始化RecyclerView
        rvCategorySongList.layoutManager = AccessibilityLinearLayoutManager(context)
        rvCategorySongList.adapter = adapter

        // 设置下拉刷新和加载更多
        srl.setOnRefreshListener {
            mViewModel?.getCategorySongList(Operation.Refresh)
        }
        srl.setOnLoadMoreListener {
            mViewModel?.getCategorySongList(Operation.LoadMore)
        }

        // 设置歌曲点击事件
        adapter.setOnItemClickListener { _, view, position ->
            KaraokePlayerManager.playSong(this@CategorySongListFragment, adapter.getItem(position), cardName)
        }

        // 设置适配器的卡片名称用于埋点
        adapter.setCardName(cardName)

        // 设置主题ID并开始加载数据
        categoryThemeBean?.theme_id?.let { themeId ->
            mViewModel?.setThemeId(themeId)
        }
    }

    /**
     * 设置错误重试监听
     */
    private fun setErrorRetryListener() {
        mBinding?.stateLayout?.setErrorRetryClickListener {
            mBinding?.stateLayout?.showLoading()
            mViewModel?.getCategorySongList(Operation.NewData)
        }
    }

    /**
     * 设置空数据监听
     */
    private fun setEmptyDataListener() {
        mBinding?.stateLayout?.setEmptyClickListener {
            NavigationUtils.navigateSafely(findNavController(), R.id.action_category_to_search)
        }
    }

    override fun initObserve() {
        super.initObserve()

        // 观察分类歌曲列表数据变化
        mViewModel?.categorySongListResult?.observe(this) { result ->
            result.onSuccess { value, operation ->
                when (operation) {
                    Operation.NewData -> {
                        adapter.setList(value?.songs)
                        updateLoadMoreState(value?.has_more == 1)
                    }
                    Operation.Refresh -> {
                        adapter.setList(value?.songs)
                        mBinding?.srl?.finishRefresh()
                        updateLoadMoreState(value?.has_more == 1)
                    }
                    Operation.LoadMore -> {
                        adapter.addData(value?.songs ?: emptyList())
                        if (value?.has_more == 1) {
                            mBinding?.srl?.finishLoadMore()
                        } else {
                            mBinding?.srl?.finishLoadMoreWithNoMoreData()
                        }
                    }
                    Operation.UpdateStatus -> {
                        adapter.setList(value?.songs)
                    }
                    else -> {}
                }

                // 更新状态布局
                if (adapter.data.isEmpty()) {
                    mBinding?.stateLayout?.showEmpty()
                    setEmptyDataListener()
                } else {
                    mBinding?.stateLayout?.showContent()
                }
            }.onFailure { _, operation ->
                when (operation) {
                    Operation.NewData -> {
                        mBinding?.stateLayout?.showError()
                        setErrorRetryListener()
                    }
                    Operation.Refresh -> {
                        mBinding?.srl?.finishRefresh()
                    }
                    Operation.LoadMore -> {
                        mBinding?.srl?.finishLoadMore()
                    }
                    Operation.UpdateStatus -> {
                        // 状态更新失败不需要特殊处理
                    }
                    else -> {}
                }
            }
        }

        // 观察播放状态变化
        KaraokeConsole.playState.observe(this) {
            adapter.notifyDataSetChanged()
        }

        // 观察收藏状态变化
        mViewModel?.collectSongChangeLiveData?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateCollectStatus(adapter.data)
            }
        }

        // 观察点歌状态变化
        mViewModel?.demandSongInfo?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateDemandStatus(adapter.data)
            }
        }
    }

    /**
     * 更新加载更多状态
     */
    private fun updateLoadMoreState(hasMore: Boolean) {
        if (hasMore) {
            mBinding?.srl?.setNoMoreData(false)
        } else {
            mBinding?.srl?.setNoMoreData(true)
        }
    }

    override fun initRequestData() {
        mViewModel?.getCategorySongList(Operation.NewData)
    }

    override fun onDestroyView() {
        // 清理RecyclerView和Adapter，防止内存泄露
        mBinding?.rvCategorySongList?.adapter = null
        super.onDestroyView()
    }

}