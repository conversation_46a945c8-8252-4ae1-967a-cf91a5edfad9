package com.sgmw.ksongs.ui.ranklist

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentRankListBinding
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.HotRank
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.RankInfo
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager

/**
 * 各种排行榜列表
 */
class RankListFragment: BaseFrameFragment<FragmentRankListBinding, RankListViewModel>() {

    companion object {

        private const val TAG = "RankListFragment"

        private const val RANK_INFO = "rank_info"
        private const val RANK_CARD_NAME = "card_name"

        /**
         * @param rankInfo 排行榜信息， 热门榜 年代榜
         * @param cardName 父分类+榜单名称 用于埋点  比如： 排行榜|热门榜
         */
        fun newInstance(rankInfo: RankInfo,cardName:String): RankListFragment {
            return RankListFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(RANK_INFO, rankInfo)
                    putString(RANK_CARD_NAME,cardName)
                }
            }
        }
    }

    override fun needSkinApply() = true

    private val adapter = RankListAdapter()

    override fun FragmentRankListBinding.initView() {
        rvRankList.layoutManager = AccessibilityLinearLayoutManager(context)
        rvRankList.adapter = adapter
        // 设置列表请求类型
        val rankInfo = arguments?.getSerializable(RANK_INFO) as? RankInfo
        val rankCardName = arguments?.getString(RANK_CARD_NAME,"" ) ?: ""
        adapter.setCardName(rankCardName)
        mViewModel?.setRankInfo(rankInfo ?: HotRank)

        srl.setOnRefreshListener {
            mViewModel?.getMultiPlaylist(Operation.Refresh)
        }
        srl.setOnLoadMoreListener {
            mViewModel?.getMultiPlaylist(Operation.LoadMore)
        }
        adapter.setOnItemClickListener { _, view, position ->
            KaraokePlayerManager.playSong(this@RankListFragment, adapter.getItem(position),rankCardName)
        }
    }

    private fun setErrorEntryListener() {
        mBinding?.stateLayout?.setErrorRetryClickListener {
            mBinding?.stateLayout?.showLoading()
            mViewModel?.getMultiPlaylist(Operation.NewData)
        }
    }

    /**
     * 设置空数据监听
     */
    private fun setEmptyDataListener() {
        mBinding?.stateLayout?.setEmptyClickListener {
            NavigationUtils.navigateSafely(findNavController(), R.id.action_hot_topic_to_search)
        }
    }

    override fun initObserve() {
        super.initObserve()
        mViewModel?.rankListResult?.observe(this) {
            it.onSuccess { value, operation ->
                when (operation) {
                    Operation.NewData -> {
                        adapter.setList(it.value?.songs)
                        if (value?.has_more == 1) {
                            mBinding?.srl?.setNoMoreData(false)
                            mBinding?.rvRankList?.setHasMoreData(true)
                            Log.d(TAG, "NewData: 设置还有更多数据")
                        } else {
                            mBinding?.srl?.setNoMoreData(true)
                            mBinding?.rvRankList?.setHasMoreData(false)
                            Log.d(TAG, "NewData: 设置没有更多数据")
                        }
                    }
                    Operation.Refresh -> {
                        adapter.setList(it.value?.songs)
                        mBinding?.srl?.finishRefresh()
                        if (value?.has_more == 1) {
                            mBinding?.srl?.setNoMoreData(false)
                            mBinding?.rvRankList?.setHasMoreData(true)
                            Log.d(TAG, "Refresh: 设置还有更多数据")
                        } else {
                            mBinding?.srl?.setNoMoreData(true)
                            mBinding?.rvRankList?.setHasMoreData(false)
                            Log.d(TAG, "Refresh: 设置没有更多数据")
                        }
                    }
                    Operation.LoadMore -> {
                        adapter.addData(it.value?.songs ?: emptyList())
                        Log.d(TAG, "LoadMore: has_more = ${value?.has_more}")
                        if (value?.has_more == 1) {
                            mBinding?.srl?.finishLoadMore()
                            mBinding?.rvRankList?.setHasMoreData(true)
                            Log.d(TAG, "LoadMore: 设置还有更多数据")
                        } else {
                            mBinding?.srl?.finishLoadMoreWithNoMoreData()
                            mBinding?.rvRankList?.setHasMoreData(false)
                            Log.d(TAG, "LoadMore: 设置没有更多数据")
                        }
                    }
                    Operation.UpdateStatus -> {
                        adapter.setList(it.value?.songs)
                    }
                    else -> {}
                }
                if (adapter.data.isEmpty()) {
                    mBinding?.stateLayout?.showEmpty()
                    setEmptyDataListener()
                } else {
                    mBinding?.stateLayout?.showContent()
                }
            }.onFailure { _, operation ->
                when (operation) {
                    Operation.NewData -> {
                        mBinding?.stateLayout?.showError()
                        setErrorEntryListener()
                    }
                    Operation.Refresh -> {
                        mBinding?.srl?.finishRefresh()
                        showToast(R.string.refresh_failed)
                    }
                    Operation.LoadMore -> {
                        mBinding?.srl?.finishLoadMore()
                        showToast(R.string.load_more_failed)
                    }
                    Operation.UpdateStatus -> {
                    }
                    else -> {}
                }
            }
        }

        KaraokeConsole.playState.observe(this) {
            adapter.notifyDataSetChanged()
        }

        mViewModel?.collectSongChangeLiveData?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateCollectStatus(adapter.data)
            }
        }
        mViewModel?.demandSongInfo?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateDemandStatus(adapter.data)
            }
        }
    }

    override fun initRequestData() {
        mViewModel?.getMultiPlaylist(Operation.NewData)
    }



}