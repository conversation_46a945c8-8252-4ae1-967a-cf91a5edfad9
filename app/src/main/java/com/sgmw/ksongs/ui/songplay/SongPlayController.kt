package com.sgmw.ksongs.ui.songplay

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collect
import com.autoai.baseline.support.skincore.SkinManager
import com.sgmw.common.ktx.loadCornerImage
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.GlideUtil
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.MainActivity
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.Constant
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.databinding.ActivityMainBinding
import com.sgmw.ksongs.databinding.FragmentSongPlayBinding
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.manager.AudioFocusHelper
import com.sgmw.ksongs.manager.CarManager
import com.sgmw.ksongs.manager.GlobalConfigHelper
import com.sgmw.ksongs.manager.PermissionHelper
import com.sgmw.ksongs.manager.StatusBarManager
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.phonestatus.PhoneStatusManager
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.dialog.ConfirmDialogFragment
import com.sgmw.ksongs.ui.dialog.ScoreDialogFragment
import com.sgmw.ksongs.ui.dialog.TuningDialogFragment
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.playlist.SongListDialogFragment
import com.sgmw.ksongs.ui.playlist.SungListManager
import com.sgmw.ksongs.utils.ScoreTransformUtils
import com.sgmw.ksongs.utils.TimeUtils
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.tme.ktv.intonation.SingIntonationViewer
import com.tme.ktv.intonation.decorate.SingIntonationDelegate
import com.tme.ktv.intonation.view.PerfectAnimView
import com.tme.ktv.intonation.view.SimpleScoreBar
import com.tme.ktv.lyric.LyricController
import com.tme.ktv.lyric.LyricInitializer
import com.tme.ktv.lyric.widget.LyricView
import com.tme.ktv.player.api.KaraokePlayRequest
import com.tme.ktv.player.api.KaraokePlayer
import com.tme.ktv.player.api.KaraokePlayerListener
import com.tme.ktv.player.api.KaraokeScoreInfo
import com.tme.ktv.player.api.PlayEndInfo
import com.tme.ktv.video.api.VideoState
import com.tme.ktv.widget.KGDialog
import kotlinx.coroutines.delay
import ksong.support.audio.score.ScoreResult
import ksong.support.audio.score.ScoreResultRequester
import ksong.support.audio.score.SingCompetitor
import ksong.support.audio.score.multiscore.MultiScoreResultInfo

/**
 * @author: 董俊帅
 * @time: 2025/4/24
 * @desc: 歌曲播放页面
 * 依附MainActivity,解决SDK视频组件(KtvVideoLayout)不允许多次销毁重建的限制
 */
class SongPlayController(
    private val context: MainActivity,
    private val container: ViewGroup,
    private val mMainBinding: ActivityMainBinding
) : SongPlayViewModel.BrightnessGestureCallback {

    companion object {
        private const val TAG = "SongPlayController"
    }

    private var mBinding: FragmentSongPlayBinding? = null
    private var mSongInfo: SongInfoBean = SongInfoBean()
    private var mSameSong = false
    private var mViewModel: SongPlayViewModel? = null
    private var mainViewModel: MainViewModel? = null

    /** 埋点：浏览起始时间 */
    private var mStartTime = -1L

    /** 打分和音准器用到的组件 start */
    private var mIntonationDelegate: SingIntonationDelegate? = null
    private var containerView: ViewGroup? = null
    private var intonationViewer: SingIntonationViewer? = null
    private var mPkScoreBar: SimpleScoreBar? = null
    private var perfectAnimView: PerfectAnimView? = null
    private val isPollMode: Boolean = false
    /** 打分和音准器用到的组件 end */

    /** 跳过前奏相关组件 start */
    private var mSkipPreludeHandler: Handler? = null
    private var mSkipPreludeCountDownRunnable: Runnable? = null
    private var mSkipPreludeRemainingTime: Int = 0
    private var mSkipPreludeFinishTime: Long = 0L
    /** 跳过前奏相关组件 end */

    // 歌曲前奏是否少于3秒，如果是，则不展示倒计时按钮
    private var isLessThan3Seconds = false

    // 前奏的实际时长（秒），用于决定倒计时点的显示策略
    private var preludeDurationSeconds = 0

    // 是否已经获取到前奏时长信息，用于避免时序问题
    private var hasPreludeInfo = false

    /** 标记当前是否处于隐藏状态（1px模式） */
    private var isViewHidden: Boolean = false

    /** 保存原始的LayoutParams，用于恢复 */
    private var originalLayoutParams: ViewGroup.LayoutParams? = null

    //全局播放器
    private val mPlayer: KaraokePlayer by lazy {
        KaraokePlayerManager.getPlayer()
    }

    private val mScoreListenerImpl: ScoreListenerImpl by lazy {
        ScoreListenerImpl()
    }

    private val mKtvMidiCallbackImpl: KtvMidiCallbackImpl by lazy {
        KtvMidiCallbackImpl()
    }

    private val mLyricController: LyricController by lazy {
        LyricController.getInstance()
    }

    // 歌词监听器实例，用于处理歌词文本更新和倒计时回调
    private var mLyricListener: LyricView.LyricListener? = null

    // 重播标志位，用于控制重播期间lyricDotView的显示状态
    // 解决重播时lyricDotView闪现的问题：由于LyricController没有removeLyricListener方法，
    // 旧的监听器仍会回调导致lyricDotView重新显示，通过此标志位在重播期间阻止显示
    private var isReplayingFlag: Boolean = false

    init {
        initViewModel()
        initView()
        initObserve()
        initSkipPreludeHandler()
    }

    private fun initView() {
        mBinding = FragmentSongPlayBinding.inflate(LayoutInflater.from(context), container, false)
        container.addView(mBinding?.root)
        mBinding?.root?.visibility = View.GONE

        mBinding?.let { binding ->
            Log.d(TAG, "FragmentSongPlayBinding initView mSameSong: $mSameSong")
            if (mSameSong) {
                updateViewState(StateLayoutEnum.PLAYING)
            } else {
                updateViewState(StateLayoutEnum.LOADING)
            }

            initLoadingView(binding)
            Log.d(TAG, "FragmentSongPlayBinding setKtvVideoLayout")
            mPlayer.setKtvVideoLayout(binding.videoLayout)

            binding.ivLoadBack.setOnSingleClickListener {
                hide()
            }
            binding.ivErrBack.setOnSingleClickListener {
                KaraokePlayerManager.stop()
                hide()
            }
            binding.tvRetry.setOnSingleClickListener {
                if (mViewModel?.currPlayError?.value == -1100 || mViewModel?.currPlayError?.value == 400) {
                    Log.d(TAG, "没网络，要刷新")
                    loadSong()
                } else {
                    Log.d(TAG, "播放错误，要刷新")
                    loadSong()
                }
            }
            binding.tvMicPermission.setOnSingleClickListener {
                PermissionHelper.checkPermission {
                    if (it) {
                        KaraokePlayerManager.replay()
                    }
                }
            }
            initScoreView()
            initSetting()
            initSetBrightnessAndVolume()
            initSkipPreludeView()
        }
    }

    /**
     * 初始化加载中界面
     */
    private fun initLoadingView(binding: FragmentSongPlayBinding) {
        binding.tvLoadSongName.text = mSongInfo.song_name
        binding.ivLoadSong.loadCornerImage(mSongInfo.album_img, R.dimen.dp_50)
    }

    /**
     * 初始化亮度和音量设置面板
     */
    private fun initSetBrightnessAndVolume() {
        mViewModel?.let {
            mBinding?.clSinging?.setVideoGestureListener(it)
        }
    }

    /**
     * 初始化打分和音准器用到的组件
     */
    private fun initScoreView() {
        mBinding?.let {
            containerView = it.flScore
            intonationViewer = it.playerFloatIntonationViewer
            mPkScoreBar = it.pkScoreBar
            perfectAnimView = it.karaokePerfectView
            mIntonationDelegate =
                SingIntonationDelegate(
                    containerView,
                    intonationViewer,
                    mPkScoreBar,
                    perfectAnimView,
                    "http://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960",
                    isPollMode
                )
        }
        mPlayer.apply {
            addOnScoreListener(mScoreListenerImpl)
            addCallback(mKtvMidiCallbackImpl)
            addCallback(mLyricController)
        }
    }

    /**
     * 初始化设置面板
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun initSetting() {
        mBinding?.run {
            // 不允许滑动
            seekBarProgress.isEnabled = false
            // 初始显示操作面板
            hideControlLayout()
            // 为所有可交互控件添加点击监听
            setControlListeners()
        }
    }

    /**
     * 为所有可交互控件添加点击监听
     */
    private fun setControlListeners() {
        mBinding?.run {
            ivSettingBack.setOnSingleClickListener {
                Log.d(TAG, "ivSettingBack.setOnSingleClickListener")
                hide()
            }
            itemVocalsSwitch.setOnSingleClickListener {
                Log.d(TAG, "itemVocalsSwitch.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
                mViewModel?.switchVocals()
            }
            itemMvSwitch.setOnSingleClickListener {
                Log.d(TAG, "itemMvSwitch.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
                mViewModel?.switchMv()
            }
            itemScoreSwitch.setOnSingleClickListener {
                Log.d(TAG, "itemScoreSwitch getScoreOpenStatus: ${mViewModel?.getScoreOpenStatus()}")
                mViewModel?.resetHideTimer()
                if (mViewModel?.getScoreOpenStatus() == true) {
                    mViewModel?.switchScore(false)
                } else {
                    showOpenScoreDialog()
                }
            }
            itemTuningSwitch.setOnSingleClickListener {
                Log.d(TAG, "itemTuningSwitch.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
                showTuningDialog()
            }
            itemPitcherSwitch.setOnSingleClickListener {
                Log.d(TAG, "itemPitcherSwitch.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
                mViewModel?.switchPitcher()
            }
            itemMicSwitch.setOnSingleClickListener {
                Log.d(TAG, "itemMicSwitch.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
            }
            ivCollect.setOnSingleClickListener {
                Log.d(TAG, "ivCollect.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
                mViewModel?.switchCollect(songInfo = mSongInfo)
            }
            ivReplay.setOnSingleClickListener {
                Log.d(TAG, "ivReplay.setOnSingleClickListener")
                SensorsDataManager.trackClickEvent(
                    BigDataConstants.EVENT_CODE_WESING_SONG_REPLAY,
                    BigDataConstants.EVENT_NAME_WESING_SONG_REPLAY,
                    newpPropertiesInfo = mapOf(
                        BigDataConstants.MUSIC_NAME to mSongInfo.song_name,
                        BigDataConstants.SINGER to mSongInfo.singer_name
                    )
                )
                if (PhoneStatusManager.isInCall()) return@setOnSingleClickListener
                mViewModel?.resetHideTimer()

                // 设置重播标志位，阻止重播期间lyricDotView的显示
                // 解决BUG：重播时旧的LyricListener回调会导致lyricDotView重新显示
                isReplayingFlag = true
                Log.d(TAG, "ivReplay: set isReplayingFlag = true to prevent lyricDotView flashing")

                mSongInfo.isPlaying = true
                mSongInfo.isPlayingState = true
                PlayListManager.addDemandSongInfo(DemandSongInfo(songInfo = mSongInfo), isPlayEnd = true)
                KaraokeConsole.currSongInfo = mSongInfo
                KaraokeConsole.currSongInfoLiveData.postValue(mSongInfo)
                mainViewModel?.lyricLiveData?.value?.let {
                    Log.d(TAG, "initObserve lyricLiveData: $it -------111")
                    initLyric(it)
                }
                KaraokePlayerManager.replay()
            }
            ivPlayStatus.setOnSingleClickListener {
                mViewModel?.resetHideTimer()
                val isPlaying = mPlayer.playState == VideoState.STATE_PLAYING
                Log.d(TAG, "ivPlayStatus.setOnSingleClickListener isPlaying: $isPlaying")
                if (isPlaying) {
                    KaraokePlayerManager.pause()
                    SensorsDataManager.trackClickEvent(
                        BigDataConstants.EVENT_CODE_WESING_SONG_PAUSE,
                        BigDataConstants.EVENT_NAME_WESING_SONG_PAUSE
                    )
                    SensorsDataManager.trackSongInfoEvent()
                } else {
                    if (PhoneStatusManager.isInCall()) return@setOnSingleClickListener
                    KaraokePlayerManager.resume()
                    SensorsDataManager.trackClickEvent(
                        BigDataConstants.EVENT_CODE_WESING_SONG_CONTINUE,
                        BigDataConstants.EVENT_NAME_WESING_SONG_CONTINUE
                    )
                }
            }
            ivPlayNext.setOnSingleClickListener {
                Log.d(TAG, "ivPlayNext.setOnSingleClickListener")
                mViewModel?.allDemandSongInfoLiveData?.value?.let {
                    if (it.isNotEmpty()) {
                        Log.d(TAG, "ivPlayNext.setOnSingleClickListener isNotEmpty")
                        mViewModel?.resetHideTimer()
                        KaraokePlayerManager.playNextSong()
                        SensorsDataManager.trackSongInfoEvent()
                    } else {
                        Log.d(TAG, "ivPlayNext.setOnSingleClickListener isEmpty")
                        KaraokePlayerManager.stop()
                        // 没有下一曲，只有当前播放的歌曲，点击下一曲，就把当前的歌曲添加到已唱列表
                        PlayListManager.updateSungList(mSongInfo.song_id)
                        hide()
                    }
                }
            }
            ivPlayListIcon.setOnSingleClickListener {
                Log.d(TAG, "ivPlayListIcon.setOnSingleClickListener")
                mViewModel?.resetHideTimer()
                showDemandListDialog()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initObserve() {
        mViewModel?.run {
            isVocalsOpen.observe(context) { isOpen ->
                Log.d(TAG, "initObserve isVocalsOpen: $isOpen")
                mBinding?.run {
                    if (isOpen) {
                        itemVocalsSwitch.setIcon(R.mipmap.icon_48_vocal_accompaniment)
                        itemVocalsSwitch.setText(context.getString(R.string.play_vocals_open))
                        itemVocalsSwitch.contentDescription = context.getString(R.string.play_vocals_open)
                    } else {
                        itemVocalsSwitch.setIcon(R.mipmap.icon_48_vocal_accompaniment_close)
                        itemVocalsSwitch.setText(context.getString(R.string.play_vocals_close))
                        itemVocalsSwitch.contentDescription = context.getString(R.string.play_vocals_close)
                    }
                }
            }
            isMvOpen.observe(context) { isOpen ->
                Log.d(TAG, "initObserve isMvOpen: $isOpen")
                mBinding?.run {
                    if (isOpen) {
                        itemMvSwitch.setText(context.getString(R.string.play_mv_open))
                        itemMvSwitch.setIcon(R.mipmap.icon_48_mv)
                        if (TextUtils.isEmpty(KaraokeConsole.shouldShowPicUrl.value)) {
                            ivShowPicUrl.visibility = View.GONE
                            videoLayout.visibility = View.VISIBLE
                        } else {
                            ivShowPicUrl.visibility = View.VISIBLE
                            videoLayout.visibility = View.GONE

                            val placeHolder = R.mipmap.bg_song_play_defalut
                            GlideUtil.loadImageWithPlaceholderAndError(
                                context,
                                shouldShowPicUrl.value ?: mSongInfo.mv_cover,
                                ivShowPicUrl,
                                placeHolder,
                                placeHolder
                            )
                        }
                        switchLyricModel(Constant.LYRIC_KTV_MODEL)
                        itemMvSwitch.contentDescription = context.getString(R.string.play_mv_open)
                    } else {
                        itemMvSwitch.setText(context.getString(R.string.play_mv_close))
                        itemMvSwitch.setIcon(R.mipmap.icon_48_mv_close)
                        ivShowPicUrl.visibility = View.VISIBLE
                        videoLayout.visibility = View.GONE
                        val placeHolder = R.mipmap.bg_song_play_defalut
                        GlideUtil.loadImageWithPlaceholderAndError(
                            context,
                            shouldShowPicUrl.value ?: mSongInfo.mv_cover,
                            ivShowPicUrl,
                            placeHolder,
                            placeHolder
                        )
                        switchLyricModel(Constant.LYRIC_LINES_MODEL)
                        itemMvSwitch.contentDescription = context.getString(R.string.play_mv_close)
                    }
                }

            }
            isScoreOpen.observe(context) { isOpen ->
                Log.d(TAG, "initObserve isScoreOpen: $isOpen")
                mBinding?.run {
                    if (isOpen) {
                        itemScoreSwitch.setIcon(R.mipmap.icon_48_recommend)
                        itemScoreSwitch.setText(context.getString(R.string.play_score_open))
                        itemScoreSwitch.contentDescription = context.getString(R.string.play_score_open)
                    } else {
                        itemScoreSwitch.setIcon(R.mipmap.icon_48_recommend_close)
                        itemScoreSwitch.setText(context.getString(R.string.play_score_close))
                        itemScoreSwitch.contentDescription = context.getString(R.string.play_score_close)
                    }
                }
            }
            isPitcherOpen.observe(context) { isOpen ->
                Log.d(TAG, "initObserve isPitcherOpen: $isOpen")
                mBinding?.run {
                    if (isOpen) {
                        itemPitcherSwitch.setText(context.getString(R.string.play_pitcher_open))
                        itemPitcherSwitch.setIcon(R.mipmap.icon_48_music)
                        flScore.visibility = View.VISIBLE
                        itemPitcherSwitch.contentDescription = context.getString(R.string.play_pitcher_open)
                    } else {
                        itemPitcherSwitch.setText(context.getString(R.string.play_pitcher_close))
                        itemPitcherSwitch.setIcon(R.mipmap.icon_48_music_close)
                        flScore.visibility = View.GONE
                        itemPitcherSwitch.contentDescription = context.getString(R.string.play_pitcher_close)
                    }
                }
            }
            isCollect.observe(context) { isCollect ->
                Log.d(TAG, "initObserve isCollect: $isCollect")
                mSongInfo.isCollect = isCollect
                mBinding?.run {
                    if (isCollect) {
                        ivCollect.setImageResource(R.mipmap.icon_72_collected)
                        ivCollect.contentDescription = context.getString(R.string.play_dis_collect)
                    } else {
                        ivCollect.setImageResource(R.mipmap.icon_72_collect)
                        ivCollect.contentDescription = context.getString(R.string.play_collect)
                    }
                }
            }
            playState.observe(context) { playState ->
                val isPlaying = playState == VideoState.STATE_PLAYING
                val isHavePermission = PermissionHelper.isHaveMicrophonePermission()
                val isVolumeMuted = CarManager.isVolumeMuted()
                Log.d(TAG, "initObserve isPlaying: $isPlaying isHavePermission: $isHavePermission isVolumeMuted: $isVolumeMuted")
                mBinding?.run {
                    if (isPlaying) {
                        ivPlayStatus.setImageResource(R.mipmap.icon_72_pause)
                        ivPlayStatus.contentDescription = context.getString(R.string.play_stop_play)
                    } else {
                        ivPlayStatus.setImageResource(R.mipmap.icon_72_play)
                        ivPlayStatus.contentDescription = context.getString(R.string.play_start_play)
                    }
                }
                if (isPlaying && isHavePermission && !isVolumeMuted) {
                    AudioFocusHelper.setKaraokeEnableOn()
                }
            }
            allDemandSongInfoLiveData.observe(context) {
                Log.d(TAG, "initObserve allDemandSongInfoLiveData size: ${it.size}")
                mBinding?.run {
                    if (it.isNotEmpty()) {
                        llNextSong.visibility = View.VISIBLE
                        tvNextSongName.text = it.first().songInfo.song_name
                    } else {
                        llNextSong.visibility = View.GONE
                    }
                }
            }
            allDemandSongInfoWithPlayingLiveData.observe(context) {
                Log.d(TAG, "initObserve allDemandSongInfoWithPlayingLiveData size: ${it.size}")
                mBinding?.run {
                    if (it.isNotEmpty()) {
                        tvPlayListCount.visibility = View.VISIBLE
                        if (it.size > 99) {
                            tvPlayListCount.text = context.getString(R.string.number_99_more)
                        } else {
                            tvPlayListCount.text = it.size.toString()
                        }
                    } else {
                        tvPlayListCount.visibility = View.GONE
                        llNextSong.visibility = View.GONE
                    }
                }
            }
            // 使用 SharedFlow 替代 AutoCleanLiveData，避免粘性问题
            context.lifecycleScope.launch {
                preLoadProgress.collect { progress ->
                    mBinding?.run {
                        Log.d(TAG, "preLoadProgress progress：$progress")
                        tvProgress.text = context.getString(R.string.play_load_progress, progress)
                        pbSong.progress = progress
                        lyricView.visibility = View.GONE
                        if (progress == 100) {
                            updateViewState(StateLayoutEnum.PLAYING)
                            if (KaraokeConsole.isPitcherOpen.value == true || KaraokeConsole.isPitcherOpen.value == null) {
                                flScore.visibility = View.VISIBLE
                            } else {
                                flScore.visibility = View.INVISIBLE
                            }
                        } else {
                            updateViewState(StateLayoutEnum.LOADING)
                            flScore.visibility = View.INVISIBLE
                        }
                    }
                }
            }
            playingSongInfoLiveData.observe(context) {
                mBinding?.run {
                    it?.songInfo?.let { songInfo ->
                        Log.d(TAG, "initObserve: $songInfo")
                        initSettingState(songInfo)
                    }
                }
            }
            currPlayError.observeForever { errorCode ->
                Log.e(TAG, "currPlayError: $errorCode")
                when (errorCode) {
                    -1100, 400 -> {
                        updateViewState(StateLayoutEnum.NO_NET)
                    }
                    3103 -> {
                        Log.e(TAG, "currPlayError: 3103 预加载取消")
                    }
                    else -> {
                        updateViewState(StateLayoutEnum.ERROR)
                    }
                }

            }
            shouldShowPicUrl.observe(context) { url ->
                Log.d(TAG, "shouldShowPicUrl: $url")
                mBinding?.let { binding ->
                    if (TextUtils.isEmpty(url)) {
                        binding.videoLayout.visibility = View.VISIBLE
                        binding.ivShowPicUrl.visibility = View.GONE
                    } else {
                        val placeHolder = R.mipmap.bg_song_play_defalut
                        GlideUtil.loadImageWithPlaceholderAndError(
                            context,
                            url,
                            binding.ivShowPicUrl,
                            placeHolder,
                            placeHolder
                        )
                        binding.videoLayout.visibility = View.INVISIBLE
                        binding.ivShowPicUrl.visibility = View.VISIBLE
                    }
                }
            }
            // 使用 SharedFlow 替代 AutoCleanLiveData，避免粘性问题
            context.lifecycleScope.launch {
                currentTime.collect { currentTime ->
                    mBinding?.run {
                        tvPlayTime.text = TimeUtils.formatMillisecondsWithRound(currentTime)
                        KaraokeConsole.duration.value?.let { duration ->
                            tvEndTime.text = TimeUtils.formatMillisecondsWithRound(duration)
                            seekBarProgress.max = duration.toInt()
                            seekBarProgress.progress = currentTime.toInt()
                        }
                    }
                }
            }
            showControlBar.observe(context) {
                Log.d(TAG, "showControlBar: $it")
                if (it) {
                    showControlLayout()
                } else {
                    hideControlLayout()
                }
            }
            brightnessData.observe(context) {
                Log.d(TAG, "brightnessData: $it")
                mBinding?.run {
                    brightSetting.setProgress(it)
                }
            }

            screenData.observe(context) { brightness ->
                Log.d(TAG, "screenData: $brightness")
                // 注释：手势滑动过程中不设置实际亮度，只在手势结束时设置
                // 这样避免频繁调用系统API，保证UI流畅响应
                // 实际亮度设置在 SongPlayViewModel.onEndChangeBright() 中处理
            }

            volumeData.observe(context) {
                Log.d(TAG, "volumeData: $it")
                mBinding?.run {
                    volumeSetting.setProgress(it)
                }
            }
            songInfoLiveData.observe(context) { songInfo ->
                Log.d(TAG, "mSongInfo.song_id: ${mSongInfo.song_id} song_name:${mSongInfo.song_name}")
                songInfo?.let {
                    Log.d(TAG, "it.song_id: ${it.song_id} song_name:${it.song_name}")
                    if (mSongInfo.song_id != it.song_id) {
                        mSongInfo = it
                        mSameSong = false
                        mViewModel?.resetHideTimer()
                        mBinding?.let { binding ->
                            binding.seekBarProgress.progress = 0
                            initLoadingView(binding)
                        }

                    }
                }

            }
        }
        mainViewModel?.lyricLiveData?.observe(context) {
            Log.d(TAG, "mainViewModel?.lyricLiveData?.observe")
            Log.d(TAG, "initObserve lyricLiveData: $it -------222")
            initLyric(it)
        }

        mainViewModel?.lyricCountDown?.observe(context) { countDownMillis ->
            mBinding?.lyricDotView?.let { lyricDotView ->
                Log.d(TAG, "lyricCountDown: $countDownMillis, hasPreludeInfo: $hasPreludeInfo, preludeDurationSeconds: $preludeDurationSeconds, isLessThan3Seconds: $isLessThan3Seconds, isReplayingFlag: $isReplayingFlag")

                // 重播期间阻止lyricDotView显示，解决重播时闪现的BUG
                if (isReplayingFlag) {
                    Log.d(TAG, "lyricCountDown: isReplayingFlag=true, prevent lyricDotView display during replay")
                    lyricDotView.visibility = View.GONE
                    return@observe
                }

                if (!hasPreludeInfo) {
                    // 还未获取到前奏信息，暂时不显示倒计时点，避免时序问题
                    lyricDotView.visibility = View.GONE
                    return@observe
                }

                if (isLessThan3Seconds) {
                    // 前奏时长小于3秒，完全不显示倒计时点
                    lyricDotView.visibility = View.GONE
                } else {
                    // 前奏时长>=3秒，根据OnLyricCountDown回调控制显示
                    when {
                        countDownMillis > 4800 -> {
                            // 距离演唱开始超过4800毫秒，显示5个点，原因是可能是损耗或消息传递导致到这里的时候已经不够5000+
                            lyricDotView.visibility = View.VISIBLE
                            lyricDotView.updateDotsBasedOnCountDown(5000) // 固定显示5个点
                        }
                        countDownMillis >= 1000 -> {
                            // 倒计时进行中（5秒-1秒），显示对应点数
                            lyricDotView.visibility = View.VISIBLE
                            lyricDotView.updateDotsBasedOnCountDown(countDownMillis)
                        }
                        else -> {
                            // 倒计时结束（小于1秒），不显示
                            lyricDotView.visibility = View.GONE
                        }
                    }
                }
            }
        }
    }

    /**
     * 初始化设置面板状态
     */
    private fun initSettingState(songInfo: SongInfoBean) {
        mBinding?.run {
            // 播放歌曲布局
            ivSingingSong.loadCornerImage(songInfo.album_img, R.dimen.dp_10)
            tvSingingSongName.text = songInfo.song_name
            //设置部分
            if (songInfo.has_ori_song) {    //原唱
                itemVocalsSwitch.isEnabled = true
                itemVocalsSwitch.alpha = 1f
                mViewModel?.initVocalsStatus()
            } else {
                itemVocalsSwitch.isEnabled = false
                itemVocalsSwitch.setText(context.getString(R.string.play_vocals_close))
                itemVocalsSwitch.alpha = 0.4f
            }
            if (mViewModel?.getMvOpenStatus() == true) {
                if (songInfo.has_mv) { //是否有MV
                    if (songInfo.mv_has_lyric) {
                        itemMvSwitch.isEnabled = false
                        itemMvSwitch.alpha = 0.4f
                    } else {
                        itemMvSwitch.isEnabled = true
                        itemMvSwitch.alpha = 1f
                    }
                    KaraokeConsole.isMvOpen.postValue(true)
                } else {
                    itemMvSwitch.isEnabled = false
                    itemMvSwitch.alpha = 0.4f
                    KaraokeConsole.isMvOpen.postValue(false)
                }
            } else {
                if (songInfo.has_mv) { //是否有MV
                    if (songInfo.mv_has_lyric) {
                        itemMvSwitch.isEnabled = false
                        itemMvSwitch.alpha = 0.4f
                    } else {
                        itemMvSwitch.isEnabled = true
                        itemMvSwitch.alpha = 1f
                    }
                } else {
                    itemMvSwitch.isEnabled = false
                    itemMvSwitch.alpha = 0.4f
                }
                KaraokeConsole.isMvOpen.postValue(false)
            }
            if (songInfo.has_midi) {    //是否支持打分
                itemScoreSwitch.isEnabled = true
                itemScoreSwitch.alpha = 1f
                mViewModel?.initScoreStatus()
            } else {
                itemScoreSwitch.isEnabled = false
                itemScoreSwitch.alpha = 0.4f
            }
            mViewModel?.initPitcherStatus()
            mViewModel?.initCollectionState(songInfo)
        }
    }

    /**
     * 加载歌曲
     */
    private fun loadSong() {
        mBinding?.let { binding ->
            updateViewState(StateLayoutEnum.LOADING)
            initLoadingView(binding)
            mPlayer.setKtvVideoLayout(binding.videoLayout)
            KaraokePlayerManager.loadSong(mSongInfo)
        }
    }

    /**
     * 显示控制布局
     */
    private fun showControlLayout() {
        mBinding?.clSetting?.let {
            if (it.visibility != View.VISIBLE) {
                it.alpha = 0f
                it.visibility = View.VISIBLE
                it.animate()
                    .alpha(1f)
                    .setDuration(100)
                    .start()
                mViewModel?.resetHideTimer()
            }
        }
    }

    /**
     * 隐藏控制布局
     */
    private fun hideControlLayout() {
        mBinding?.clSetting?.let {
            if (it.visibility == View.VISIBLE) {
                it.animate()
                    .alpha(0f)
                    .setDuration(100)
                    .withEndAction {
                        it.visibility = View.GONE
                        it.alpha = 1f // 重置alpha以便下次显示
                    }
                    .start()
            }
        }
    }

    /**
     * 将根视图缩小到1px
     * 直接修改LayoutParams为1px x 1px，简单直接的实现
     */
    private fun shrinkViewToOnePixel() {
        mBinding?.root?.let { rootView ->
            try {
                // 保存原始LayoutParams
                if (originalLayoutParams == null) {
                    originalLayoutParams = rootView.layoutParams
                }

                if (originalLayoutParams != null) {
                    // 直接修改LayoutParams为1px x 1px
                    val newLayoutParams = when (val params = originalLayoutParams!!) {
                        is ViewGroup.MarginLayoutParams -> {
                            ViewGroup.MarginLayoutParams(params).apply {
                                width = 1
                                height = 1
                            }
                        }
                        else -> {
                            ViewGroup.LayoutParams(1, 1)
                        }
                    }
                    rootView.layoutParams = newLayoutParams

                    isViewHidden = true
                    Log.d(TAG, "shrinkViewToOnePixel: layoutParams set to 1px x 1px")
                } else {
                    Log.w(TAG, "shrinkViewToOnePixel: originalLayoutParams is null")
                }
            } catch (e: Exception) {
                Log.e(TAG, "shrinkViewToOnePixel error", e)
            }
        }
    }

    /**
     * 恢复根视图到原始尺寸
     * 恢复原始LayoutParams
     */
    private fun restoreViewSize() {
        mBinding?.root?.let { rootView ->
            try {
                if (isViewHidden && originalLayoutParams != null) {
                    // 恢复原始LayoutParams
                    rootView.layoutParams = originalLayoutParams

                    // 确保视图可见
                    rootView.visibility = View.VISIBLE

                    isViewHidden = false
                    Log.d(TAG, "restoreViewSize: layoutParams restored to original")
                } else {
                    // 如果之前没有隐藏，直接设置可见
                    rootView.visibility = View.VISIBLE
                    Log.d(TAG, "restoreViewSize rootView.visibility = View.VISIBLE")
                }
            } catch (e: Exception) {
                Log.e(TAG, "restoreViewSize error", e)
            }
        }
    }

    fun onDestroyView() {
        Log.d(TAG, "onDestroyView start")
        try {
            // 重置视图状态变量
            isViewHidden = false
            originalLayoutParams = null

            // 重置重播标志位
            isReplayingFlag = false

            // 清理ViewModel
            mViewModel?.onDestroyView()

            // 清理歌词组件
            LyricInitializer().setLyricView(null).setLyricTimeLine(null)

            // 停止播放器
            KaraokePlayerManager.stop()

            // 移除播放器回调
            mPlayer.removeCallback(mKtvMidiCallbackImpl)
            mPlayer.removeCallback(mLyricController)
            mPlayer.removeOnScoreListener(mScoreListenerImpl)

            // 清理跳过前奏相关资源
            mSkipPreludeCountDownRunnable?.let { mSkipPreludeHandler?.removeCallbacks(it) }
            mSkipPreludeHandler = null
            mSkipPreludeCountDownRunnable = null

            // 清理打分和音准器组件
            mIntonationDelegate = null
            containerView = null
            intonationViewer = null
            mPkScoreBar = null
            perfectAnimView = null

            // 清理绑定
            mBinding = null

            Log.d(TAG, "onDestroyView completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during onDestroyView", e)
        }
    }

    /**
     * 初始化歌词组件
     */
    private fun initLyric(request: KaraokePlayRequest) {
        Log.d(
            TAG,
            "initLyric songInfoObject songName: ${request.songInfoObject.songName} songid: ${request.songInfoObject.songid}"
        )
        Log.d(
            TAG,
            "initLyric currSongInfo song_name: ${KaraokeConsole.currSongInfo?.song_name} song_id: ${KaraokeConsole.currSongInfo?.song_id}"
        )
        if (request.songInfoObject.songid != KaraokeConsole.currSongInfo?.song_id) {
            Log.d(TAG, "is not same song lyric, may this request is old!!!")
            return
        }

        Log.d(TAG, "initLyric: isMvHasLyric: ${request.songInfoObject.isMvHasLyric}")
        if (request.songInfoObject.isMvHasLyric == 1) {
            Log.d(TAG, "mv has lyric, need not init lyric!!!")
            mBinding?.apply {
                lyricView.visibility = View.GONE
                itemMvSwitch.isEnabled = false
                itemMvSwitch.alpha = 0.4f
                if (mViewModel?.getMvOpenStatus() == false) {
                    mViewModel?.isMvOpen?.postValue(true)
                }
            }
        } else {
            mBinding?.lyricView?.visibility = View.VISIBLE
        }
        mBinding?.lyricDotView?.visibility = View.GONE
        val lyricView = mBinding?.lyricView ?: return
        Log.d(TAG, "initLyric: mQrcBytes: ${request.songInfoObject.mQrcBytes?.size}")
        //获取当前的KaraokePlayer
        LyricInitializer()
            .setLyricView(lyricView)
            .setLyricData(request.songInfoObject.mQrcBytes, request.songInfoObject.mLrcBytes)
            .setLyricTimeLine { mPlayer.timeLineTime.toInt() }
            .setIsShowLyric(request.songInfoObject.mMvHasLyric != 1)
            //歌词初始化有轻微耗时，可异步加载。
            .build()

        // 更新打分组件
        val scoreInfo = KaraokeScoreInfo()
        scoreInfo.timeArray = LyricController.getInstance().timeArray
        Log.d(TAG, "initLyric: timeArray: ${scoreInfo.timeArray}")
        scoreInfo.sentenceCount = LyricController.getInstance().sentenceCount()
        Log.d(TAG, "initLyric: sentenceCount: ${scoreInfo.sentenceCount}")
        scoreInfo.lastSentenceEndTime = LyricController.getInstance().lastSentenceEndTime
        Log.d(TAG, "initLyric: lastSentenceEndTime: ${scoreInfo.lastSentenceEndTime}")
        mPlayer.scoreInfo = scoreInfo

        lyricView.resumeLyric()

        var lyricModel = Constant.LYRIC_KTV_MODEL
        if (mViewModel?.getMvOpenStatus() == false || !mSongInfo.has_mv) {
            lyricModel = Constant.LYRIC_LINES_MODEL
        }
        switchLyricModel(lyricModel)
        // 注册歌词监听器，处理歌词文本更新和倒计时回调
        registerLyricListener()
    }

    /**
     * 切换歌词模式
     */
    private fun switchLyricModel(model: Int) {
        Log.d(TAG, "switchLyricModel model: $model")
        if (model != Constant.LYRIC_KTV_MODEL && model != Constant.LYRIC_LINES_MODEL) {
            Log.d(TAG, "LyricMode is invalid !!!")
            return
        }
        val layoutParams = mBinding?.lyricView?.layoutParams as? ConstraintLayout.LayoutParams ?: return
        val dotViewLayoutParams = mBinding?.lyricDotView?.layoutParams as? ConstraintLayout.LayoutParams ?: return
        when (model) {
            Constant.LYRIC_KTV_MODEL -> {
                // MV模式：设置距左dp_200，距尾dp_36
                dotViewLayoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                dotViewLayoutParams.endToEnd = ConstraintLayout.LayoutParams.UNSET
                dotViewLayoutParams.marginStart = context.resources.getDimensionPixelSize(R.dimen.dp_200)
                dotViewLayoutParams.marginEnd = context.resources.getDimensionPixelSize(R.dimen.dp_36)

                layoutParams.topToTop = ConstraintLayout.LayoutParams.UNSET
                layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                layoutParams.bottomMargin = context.resources.getDimensionPixelSize(R.dimen.dp_269)
                val textSize = context.resources.getDimensionPixelSize(R.dimen.sp_60)
                mBinding?.let {
                    it.lyricView.lyricParam.textSize = textSize
                    it.lyricView.lyricParam.lightTextSize = textSize
                    it.lyricView.lyricParam.lightUnSelectTextSize = textSize
                    it.lyricView.lyricParam.lineMargin = context.resources.getDimensionPixelSize(R.dimen.dp_20)
                    if (TextUtils.isEmpty(KaraokeConsole.shouldShowPicUrl.value)) {
                        it.ivShowPicUrl.visibility = View.GONE
                    } else {
                        it.ivShowPicUrl.visibility = View.VISIBLE
                    }
                    it.flLyric.background = null
                }
            }

            Constant.LYRIC_LINES_MODEL -> {
                // 歌词模式：设置距左dp_0，距尾dp_0，水平居中
                dotViewLayoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                dotViewLayoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                dotViewLayoutParams.marginStart = context.resources.getDimensionPixelSize(R.dimen.dp_0)
                dotViewLayoutParams.marginEnd = context.resources.getDimensionPixelSize(R.dimen.dp_0)

                layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                layoutParams.bottomMargin = context.resources.getDimensionPixelSize(R.dimen.dp_0)

                val textSize = context.resources.getDimensionPixelSize(R.dimen.sp_40)
                val lyricBackGround = R.mipmap.bg_song_play_defalut
                mBinding?.let {
                    it.lyricView.lyricParam.textSize = textSize
                    it.lyricView.lyricParam.lightTextSize = textSize
                    it.lyricView.lyricParam.lightUnSelectTextSize = textSize
                    it.lyricView.lyricParam.lineMargin = context.resources.getDimensionPixelSize(R.dimen.dp_24)
                    it.ivShowPicUrl.visibility = View.VISIBLE
                    SkinManager.getInstance().setBackground(it.flLyric, lyricBackGround)
                }
            }
        }
        mBinding?.lyricView?.layoutParams = layoutParams
        mBinding?.lyricDotView?.layoutParams = dotViewLayoutParams
        mBinding?.lyricView?.switchMode(model)

        // 重要：在切换歌词模式后重新注册监听器
        // 因为switchMode可能会导致SDK内部重新初始化组件，清除之前注册的监听器
        // 这是修复lyricDotView倒计时状态丢失问题的关键步骤
        registerLyricListener()
    }

    /**
     * 注册歌词监听器
     * 用于处理歌词文本更新和倒计时回调，确保lyricDotView能正常工作
     */
    private fun registerLyricListener() {
        try {
            // 创建新的监听器实例
            mLyricListener = object : LyricView.LyricListener {
                override fun OnLyricTextUpdate(text: String?) {
                    Log.d(TAG, "OnLyricTextUpdate: $text")
                    mainViewModel?.lyricCountDown?.postValue(0)
                    mainViewModel?.lyricStrLiveData?.postValue(text)
                }

                override fun OnLyricCountDown(countDown: Int) {
                    // 跳过前奏倒计时回调，比如5000代表还有5s开始显示歌词
                    // 这个回调是lyricDotView倒计时动画的数据源
                    mainViewModel?.lyricCountDown?.postValue(countDown)
                }
            }

            // 注册新的监听器
            mLyricListener?.let { newListener ->
                LyricController.getInstance().addLyricListener(newListener)
                Log.d(TAG, "registerLyricListener: registered new listener successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "registerLyricListener failed", e)
        }
    }

    /**
     * 更新页面展示状态
     */
    private fun updateViewState(state: StateLayoutEnum) {
        Log.d(TAG, "updateViewState state: $state")
        mBinding?.run {
            when (state) {
                StateLayoutEnum.LOADING -> {
                    hideCancelSkipPrelude()
                    clLoad.visibility = View.VISIBLE
                    clSinging.visibility = View.GONE
                    clLoadErr.visibility = View.GONE
                    tvMicPermission.visibility = View.GONE
                }

                StateLayoutEnum.PLAYING -> {
                    initOpenMic()
                    clLoad.visibility = View.GONE
                    clSinging.visibility = View.VISIBLE
                    clLoadErr.visibility = View.GONE
                }

                StateLayoutEnum.ERROR -> {
                    hideCancelSkipPrelude()
                    clLoad.visibility = View.GONE
                    clSinging.visibility = View.GONE
                    clLoadErr.visibility = View.VISIBLE
                    ivErr.setImageResource(R.mipmap.icon_song_play_err)
                    tvErr.text = context.getString(R.string.play_error_str)
                    tvMicPermission.visibility = View.GONE
                }

                StateLayoutEnum.NO_NET -> {
                    hideCancelSkipPrelude()
                    clLoad.visibility = View.GONE
                    clSinging.visibility = View.GONE
                    clLoadErr.visibility = View.VISIBLE
                    ivErr.setImageResource(R.mipmap.icon_song_play_no_net)
                    tvErr.text = context.getString(R.string.play_error_no_net)
                    tvMicPermission.visibility = View.GONE
                }
            }
        }
    }

    /**
     * 初始化跳过前奏Handler
     */
    private fun initSkipPreludeHandler() {
        mSkipPreludeHandler = Handler(Looper.getMainLooper())
    }

    /**
     * 初始化跳过前奏控件
     */
    private fun initSkipPreludeView() {
        mBinding?.run {
            // 倒计时文本点击事件 - 跳过前奏
            tvSkipPreludeCountdown.setOnSingleClickListener {
                Log.d(TAG, "tvSkipPreludeCountdown clicked, seek to: $mSkipPreludeFinishTime")
                if (mSkipPreludeFinishTime > 0) {
                    mPlayer.seek(mSkipPreludeFinishTime)
                }
                hideCancelSkipPrelude()
            }

            // 关闭按钮点击事件
            ivSkipPreludeClose.setOnSingleClickListener {
                Log.d(TAG, "ivSkipPreludeClose clicked")
                hideCancelSkipPrelude()
            }
        }
    }

    /**
     * 显示跳过前奏控件并开始倒计时
     */
    private fun showCancelSkipPrelude(preludeFinishTime: Long) {
        Log.d(TAG, "showCancelSkipPrelude preludeFinishTime: $preludeFinishTime")

        // 参数校验
        if (preludeFinishTime <= 0) {
            Log.e(TAG, "showCancelSkipPrelude: invalid preludeFinishTime")
            return
        }

        // 如果已经在显示，先隐藏之前的
        if (mBinding?.clSkipPrelude?.visibility == View.VISIBLE) {
            hideCancelSkipPrelude()
        }

        mSkipPreludeFinishTime = preludeFinishTime
        // 固定从5秒开始倒计时
        mSkipPreludeRemainingTime = 5

        mBinding?.run {
            clSkipPrelude.visibility = View.VISIBLE
            updateSkipPreludeCountDownText()
            startSkipPreludeCountDownTimer()
        }
    }

    /**
     * 隐藏跳过前奏控件
     */
    private fun hideCancelSkipPrelude() {
        Log.d(TAG, "hideCancelSkipPrelude")

        // 停止倒计时
        mSkipPreludeCountDownRunnable?.let { runnable ->
            mSkipPreludeHandler?.removeCallbacks(runnable)
        }

        // 隐藏控件
        mBinding?.clSkipPrelude?.visibility = View.GONE

        // 重置状态
        mSkipPreludeFinishTime = 0L
        mSkipPreludeRemainingTime = 0
    }

    /**
     * 开始跳过前奏倒计时定时器
     */
    private fun startSkipPreludeCountDownTimer() {
        // 先清理之前的倒计时
        mSkipPreludeCountDownRunnable?.let { runnable ->
            mSkipPreludeHandler?.removeCallbacks(runnable)
        }

        mSkipPreludeCountDownRunnable = object : Runnable {
            override fun run() {
                try {
                    mSkipPreludeRemainingTime--
                    if (mSkipPreludeRemainingTime <= 0) {
                        // 倒计时结束，自动隐藏
                        hideCancelSkipPrelude()
                        return
                    }
                    updateSkipPreludeCountDownText()
                    mSkipPreludeHandler?.postDelayed(this, 1000)
                } catch (e: Exception) {
                    Log.e(TAG, "startSkipPreludeCountDownTimer error", e)
                    hideCancelSkipPrelude()
                }
            }
        }

        mSkipPreludeCountDownRunnable?.let { runnable ->
            mSkipPreludeHandler?.post(runnable)
        }
    }

    /**
     * 更新跳过前奏倒计时文本
     */
    private fun updateSkipPreludeCountDownText() {
        try {
            mBinding?.tvSkipPreludeCountdown?.text =
                context.getString(R.string.skip_prelude_countdown, mSkipPreludeRemainingTime)
        } catch (e: Exception) {
            Log.e(TAG, "updateSkipPreludeCountDownText error", e)
        }
    }

    /**
     * 麦克风权限状态
     */
    private fun initOpenMic() {
        val haveMicrophonePermission = PermissionHelper.isHaveMicrophonePermission()
        Log.d(TAG, "initOpenMic haveMicrophonePermission: $haveMicrophonePermission")
        mBinding?.tvMicPermission?.apply {
            visibility = if (haveMicrophonePermission) View.GONE else View.VISIBLE
            setOnSingleClickListener {
                PermissionHelper.showPermissionDialog {
                    if (it) {
                        KaraokePlayerManager.replay()
                    }
                }
            }
        }
    }

    private fun initViewModel() {
        mViewModel = ViewModelProvider(context as androidx.fragment.app.FragmentActivity)[SongPlayViewModel::class.java]
        mainViewModel = ViewModelProvider(context)[MainViewModel::class.java]
        // 设置亮度手势回调
        mViewModel?.setBrightnessGestureCallback(this)
    }

    fun show() {
        mStartTime = System.currentTimeMillis()
        Log.d(TAG, "show")
        KaraokePlayerManager.setShowPlayPage(true)
        StatusBarManager.setUpStatusBar(true, context, mMainBinding)

        // 恢复视图尺寸到原始大小
        restoreViewSize()

        mainViewModel?.isSongPlayViewShow?.postValue(true)
        mViewModel?.initScreenBrightness(context)
        initOpenMic()

        mBinding?.let { binding ->
            if (KaraokeConsole.shouldShowPicUrl.value.isNullOrEmpty()) {
                binding.videoLayout.visibility = View.VISIBLE
                binding.ivShowPicUrl.visibility = View.GONE
            } else {
                val placeHolder = R.mipmap.bg_song_play_defalut
                GlideUtil.loadImageWithPlaceholderAndError(
                    context,
                    KaraokeConsole.shouldShowPicUrl.value!!,
                    binding.ivShowPicUrl,
                    placeHolder,
                    placeHolder
                )
                binding.videoLayout.visibility = View.GONE
                binding.ivShowPicUrl.visibility = View.VISIBLE
            }
        }
    }

    fun hide() {
        Log.d(TAG, "hide")
        if (!isViewHidden && mStartTime > 0) {
            val mEndTime = System.currentTimeMillis() - mStartTime
            Log.d(TAG, "mEndTime: $mEndTime")
            SensorsDataManager.trackBrowseMvPage(mEndTime)
            mStartTime = -1L
        }
        if (isDialogFragmentShowing(context)) {
            dismissDialogFragment(context)
        }
        KaraokePlayerManager.setShowPlayPage(false)
        StatusBarManager.setUpStatusBar(false, context, mMainBinding)
        mViewModel?.resetScreenBrightness(context)

        // 将视图缩小到1px，但保持内部子控件布局不变
        shrinkViewToOnePixel()

        mainViewModel?.isSongPlayViewShow?.postValue(false)

        SensorsDataManager.trackSongInfoEvent()
    }

    // MainActivity onResume回调的时候调用
    fun onResume() {
        initOpenMic()
    }

    /**
     * 是否展示播放视图
     */
    fun isPlayViewShow(): Boolean {
        return !isViewHidden && mBinding?.root?.visibility == View.VISIBLE
    }

    /**
     * 播放歌曲
     */
    fun playSong(songInfo: SongInfoBean?, needReplay: Boolean = false, needShowPlaying: Boolean = true) {
        Log.d(TAG, "playSong needReplay: $needReplay needShowPlaying: $needShowPlaying")
        if (songInfo != null) {
            mSongInfo = songInfo
        }
        if (mSongInfo.song_id == KaraokeConsole.currSongInfo?.song_id
            && (mPlayer.playState == VideoState.STATE_PAUSE || mPlayer.playState == VideoState.STATE_PLAYING)
            && !needReplay
        ) {
            mSameSong = true
        } else {
            mSameSong = false
            val mid = mSongInfo.song_id
            if (TextUtils.isEmpty(mid)) {
                Log.e(TAG, "播放参数错误")
                hide()
                return
            }
            loadSong()
        }
        initSettingState(mSongInfo)
        if (needShowPlaying) {
            show()
        }
    }

    /**
     * 设置打分开关弹框
     */
    private fun showOpenScoreDialog() {
        val openScoreDialog = ConfirmDialogFragment()
        // 检查 OpenScoreDialog 是否已经显示
        val fragment = context.supportFragmentManager.findFragmentByTag("OpenScoreDialog")
        if (fragment == null) {
            openScoreDialog.setTitle(context.getString(R.string.score_dialog_title))
            openScoreDialog.setMessage(context.getString(R.string.score_dialog_message))
            openScoreDialog.setConfirmStr(context.getString(R.string.score_dialog_confirm))
            openScoreDialog.setCancelStr(context.getString(R.string.score_dialog_cancel))
            openScoreDialog.setOnConfirmListener {
                val isInCall = PhoneStatusManager.isInCall(true)
                if (isInCall) return@setOnConfirmListener

                mViewModel?.switchScore(true)
                mSongInfo.isPlaying = true
                mSongInfo.isPlayingState = true
                PlayListManager.addDemandSongInfo(DemandSongInfo(songInfo = mSongInfo), isPlayEnd = true)
                KaraokeConsole.currSongInfo = mSongInfo
                KaraokeConsole.currSongInfoLiveData.postValue(mSongInfo)
                mainViewModel?.lyricLiveData?.value?.let {
                    Log.d(TAG, "initObserve lyricLiveData: $it -------333")
                    initLyric(it)
                }
                KaraokePlayerManager.replay()
                openScoreDialog.dismiss()
            }
            // 弹出 ScoreDialogFragment
            openScoreDialog.show(context.supportFragmentManager, "OpenScoreDialog")
        }
    }

    /**
     * 设置调音弹框
     */
    private fun showTuningDialog() {
        val tuningDialogFragment = TuningDialogFragment()
        // 检查 OpenScoreDialog 是否已经显示
        val fragment = context.supportFragmentManager.findFragmentByTag("TuningDialogFragment")
        if (fragment == null) {
            // 弹出 ScoreDialogFragment
            tuningDialogFragment.show(context.supportFragmentManager, "TuningDialogFragment")
        }
    }

    /**
     * 播放列表弹框
     */
    private fun showDemandListDialog() {
        val songListDialogFragment = SongListDialogFragment()
        // 检查 OpenScoreDialog 是否已经显示
        val fragment = context.supportFragmentManager.findFragmentByTag("SongListDialogFragment")
        if (fragment == null) {
            // 弹出 ScoreDialogFragment
            songListDialogFragment.show(context.supportFragmentManager, "SongListDialogFragment")
        }
    }

    /**
     * 播放完成，弹出打分弹框
     */
    private fun showScoreDialog() {
        val scoreDialogFragment = ScoreDialogFragment().apply {
            arguments = bundleOf(ScoreDialogFragment.KEY_SCORE_DIALOG to KaraokeConsole.currSongInfo?.deepCopy())
        }
        // 检查 OpenScoreDialog 是否已经显示
        val fragment = context.supportFragmentManager.findFragmentByTag("ScoreDialogFragment")
        if (fragment == null) {
            // 弹出 ScoreDialogFragment
            scoreDialogFragment.show(context.supportFragmentManager, "ScoreDialogFragment")
        }
    }

    /**
     * 检查是否有DialogFragment正在显示
     */
    fun isDialogFragmentShowing(activity: MainActivity): Boolean {
        val fragments = activity.supportFragmentManager.fragments
        var i = 0
        for (fragment in fragments) {
            if (fragment is DialogFragment && fragment.dialog != null && fragment.dialog!!.isShowing) {
                i++
            }
        }
        Log.d(TAG, "isDialogFragmentShowing i: $i")
        for (fragment in fragments) {
            if (fragment is DialogFragment && fragment.dialog != null && fragment.dialog!!.isShowing) {
                return true
            }
        }
        return false
    }

    /**
     * 关闭当前显示的DialogFragment
     */
    fun dismissDialogFragment(activity: MainActivity) {
        val fragments = activity.supportFragmentManager.fragments
        for (i in fragments.size - 1 downTo 0) {
            val fragment = fragments[i]
            if (fragment is DialogFragment && fragment.dialog != null && fragment.dialog!!.isShowing) {
                fragment.dismiss()
                return
            }
        }
    }

    /**
     * 关闭当前显示的DialogFragment
     */
    fun dismissAllDialogFragment(activity: MainActivity) {
        val fragments = activity.supportFragmentManager.fragments
        for (i in fragments.size - 1 downTo 0) {
            val fragment = fragments[i]
            if (fragment is DialogFragment && fragment.dialog != null && fragment.dialog!!.isShowing) {
                fragment.dismiss()
            }
        }
    }

    /**
     * 处理电话状态
     */
    fun handlePhoneCall(call: Boolean) {
        if (call) {
            mBinding?.ivPlayNext?.isEnabled = false
            mBinding?.ivPlayNext?.alpha = 0.4f
        } else {
            mBinding?.ivPlayNext?.isEnabled = true
            mBinding?.ivPlayNext?.alpha = 1f
        }
    }

    /**
     * 打分监听器
     */
    inner class ScoreListenerImpl : KaraokePlayer.OnScoreListener() {

        private val multiScoreResultInfo = MultiScoreResultInfo()

        override fun onPrepareScore(competitor: SingCompetitor?, nums: Int) {
            // 打分数据就绪回调
            if (isPollMode) {
                val dataRequester = object : ScoreResultRequester {
                    override fun getMidiTime(): Long {
                        return mPlayer.timeLineTime
                    }

                    override fun getScoreResult(timestamp: Long): ScoreResult? {
                        return mPlayer.getScoreResult(timestamp)
                    }
                }
                mIntonationDelegate?.initData(mPlayer.noteItems, nums, dataRequester)
            } else {
                mIntonationDelegate?.initData(competitor)
            }
        }

        override fun onDoingScore(score: Int, level: String?, timeStamp: Int) {
            //普通打分实时回调
            if (!isPollMode) mIntonationDelegate?.postUpdateScore(score, timeStamp.toFloat(), null)
        }

        override fun onDoingMultiScore(
            lastScore: Int,
            timeStamp: Long,
            lastLongToneScore: Int,
            lastRhythmScore: Int,
            lastStableScore: Int,
            dynamicScore: Int,
            skillScore: Int,
            lastSkillTrillCount: Int,
            lastSkillGlintCount: Int,
            lastSkillTransliterationCount: Int
        ) {
            //五维打分实时回调
            multiScoreResultInfo.resetLastScore()
            multiScoreResultInfo.lastLongToneScore = lastLongToneScore
            multiScoreResultInfo.lastRhythmScore = lastRhythmScore
            multiScoreResultInfo.lastStableScore = lastStableScore
            multiScoreResultInfo.lastDynamicScore = dynamicScore
            multiScoreResultInfo.lastSkillScore = skillScore
            multiScoreResultInfo.lastSkillTrillCount = lastSkillTrillCount
            multiScoreResultInfo.lastSkillTransliterationCount = lastSkillGlintCount
            mIntonationDelegate?.postUpdateScore(lastScore, timeStamp.toFloat(), multiScoreResultInfo)
        }

        override fun onMultiScoreFinish(
            finalLongToneScore: Int,
            finalRhythmScore: Int,
            finalStableScore: Int,
            finalDynamicScore: Int,
            finalSkillScore: Int,
            lastSentenceIndex: Int,
            totalSentences: Int
        ) {
            //五维打分回调结束
            //总分需要主动获取一下
            val totalScore = mIntonationDelegate?.getTotalScore()
            val averageScore = totalScore?.div(totalSentences) ?: 0
            val level = ScoreTransformUtils.transformScore(averageScore)
            Log.d(TAG, "onMultiScoreFinish totalScore: $totalScore averageScore: $level")
            KaraokeConsole.mLevel.postValue(level)
            KaraokeConsole.mScore.postValue(totalScore.toString())
        }

        override fun onFinishScore(totalScore: Int, level: String?) {
            Log.d(TAG, "onFinishScore totalScore: $totalScore level: $level")
            KaraokeConsole.mLevel.postValue(level)
            KaraokeConsole.mScore.postValue(totalScore.toString())
        }

    }

    /**
     * 播放器方法回调
     */
    inner class KtvMidiCallbackImpl : KaraokePlayerListener() {

        override fun onPlayStart(request: KaraokePlayRequest) {
            Log.d(
                TAG,
                "onPlayStart songName:${request.songInfoObject.songName} mMvHasLyric: ${request.songInfoObject.mMvHasLyric}"
            )

            KaraokeConsole.shouldShowPicUrl.postValue("")

            // 重置前奏信息标志位
            hasPreludeInfo = false
            isLessThan3Seconds = false
            preludeDurationSeconds = 0

            mainViewModel?.lyricLiveData?.postValue(request)
            // 修复：移除错误的removeFromSungListById调用
            // 原因：这与PlayListManager.updatePlayingSongInfo中的逻辑冲突
            // updatePlayingSongInfo已经负责将歌曲从已点列表移动到已唱列表
            // 在onPlayStart中再次尝试从已唱列表移除会导致时序冲突和歌曲丢失
            // SungListManager.removeFromSungListById(request.songInfoObject.mid) // 已移除

            // 跳过前奏倒计时需求，#610272 【应用开发】【K歌】【播放页-播放页功能】播放页功能增加跳过前奏功能
            ioLaunch {
                // SDK文档要求，延迟500ms后才可以获取preludeFinishTime
                // https://karaoketv.coding.net/p/ktvsdk3/wiki/20
                delay(500)
                mainLaunch {
                    val preludeFinishTime = mPlayer.preludeFinishTime
                    Log.d(TAG, "preludeFinishTime: $preludeFinishTime")

                    // 计算前奏的实际时长（秒）
                    // preludeFinishTime是距离前奏结束时间前5秒的时间距离
                    // 前奏实际时长 = (preludeFinishTime + 5000) / 1000
                    preludeDurationSeconds = ((preludeFinishTime + 5000) / 1000).toInt()
                    isLessThan3Seconds = preludeDurationSeconds < 3
                    val isMoreThan10Seconds = preludeDurationSeconds > 10
                    hasPreludeInfo = true // 标记已获取到前奏信息

                    Log.d(TAG, "preludeDurationSeconds: $preludeDurationSeconds, isLessThan3Seconds: $isLessThan3Seconds, " +
                            "hasPreludeInfo: $hasPreludeInfo, isMoreThan10Seconds: $isMoreThan10Seconds")

                    // 前奏时长大于10秒，才显示跳过前奏控件
                    if (isMoreThan10Seconds) {
                        // 显示跳过前奏控件并开始倒计时
                        showCancelSkipPrelude(preludeFinishTime)
                    }
                }
            }
        }

        override fun onLyricResourceChanged(request: KaraokePlayRequest) {
            super.onLyricResourceChanged(request)
            Log.d(TAG, "onLyricResourceChanged")
            mIntonationDelegate?.startMidi(mPlayer.timeLineTime)
        }


        override fun onPlayPositionChange(
            mKtvPlayRequest: KaraokePlayRequest,
            currentTime: Long,
            duration: Long
        ) {
            if (!KGDialog.isActivityDestroyed(context)) {
                mIntonationDelegate?.seekMidi(currentTime)
            }
            KaraokeConsole.emitCurrentTime(currentTime)
            KaraokeConsole.duration.postValue(duration)
        }

        override fun onResume(mKtvPlayRequest: KaraokePlayRequest) {
            Log.d(TAG, "call onResume")
            // 每次播放都会走onResume 故在这里发送 k歌状态
            mIntonationDelegate?.startMidi(mPlayer.timeLineTime)
            KaraokeConsole.playState.postValue(mPlayer.playState)

            // 重置重播标志位，允许lyricDotView正常显示
            // 在歌曲开始播放后重置标志位，确保正常的倒计时功能不受影响
            if (isReplayingFlag) {
                isReplayingFlag = false
                Log.d(TAG, "onResume: reset isReplayingFlag = false, allow lyricDotView normal display")
            }
        }

        override fun onPause(mKtvPlayRequest: KaraokePlayRequest) {
            Log.d(TAG, "onPause")
            // 调用pause 会走pause 给vr也发送下暂停状态
            mIntonationDelegate?.stopMidi()
            AudioFocusHelper.setKaraokeEnableOff()
            KaraokeConsole.playState.postValue(mPlayer.playState)
        }

        override fun onBufferingStart(request: KaraokePlayRequest?) {
            Log.d(TAG, "onBufferingStart")
            mIntonationDelegate?.onBuffingStart()
            mainLaunch {
                showToast(R.string.play_buffer_start)
            }
        }

        override fun onBufferingEnd(request: KaraokePlayRequest?) {
            Log.d(TAG, "onBufferingEnd")
            mIntonationDelegate?.onBufferingEnd()
        }

        override fun onPlayEnd(request: KaraokePlayRequest, endInfo: PlayEndInfo) {
            Log.e(
                TAG,
                "onPlayEnd songName: ${request.songInfoObject.songName}, state: ${mPlayer.playState}"
            )
            mainLaunch {
                mIntonationDelegate?.onSongPlayComplete()
                mIntonationDelegate?.stopMidi()
                mIntonationDelegate?.releaseMidi()
                AudioFocusHelper.setKaraokeEnableOff()
                KaraokeConsole.playState.postValue(mPlayer.playState)
                val isScoreOpen = MMKVUtils.getBoolean(MMKVConstant.SCORE_OPEN_STATUE, false)
                if (endInfo.isPlayFinish) {
                    KaraokeConsole.duration.value?.let {
                        KaraokeConsole.emitCurrentTime(it)
                    }
                    PlayListManager.updateSungList(request.songInfoObject.mid)
                    if (isScoreOpen) {
                        showScoreDialog()
                    } else {
                        // 新增逻辑，若唱完后，没有打开打分开关
                        // 存在下一曲，则直接播放下一曲，若无下一曲，则退出播放页
                        val nextSong = PlayListManager.getNextPlaySongInfo()
                        if (nextSong != null) {
                            KaraokePlayerManager.playNextSong(isAutoPlayNext = true)
                        } else {
                            hide()
                        }
                    }

                    if (isPlayViewShow()) {
                        SensorsDataManager.trackSongInfoEvent()
                    }
                }
                mainViewModel?.lyricStrLiveData?.postValue("")
            }
        }

        override fun onSeekComplete(request: KaraokePlayRequest, position: Long) {
            Log.d(TAG, "onSeekComplete position $position ")
            mIntonationDelegate?.seekMidi(position)
        }

        /**
         * 调用{@link KaraokePlayer#play} 方法后回调
         */
        override fun onPrePlay(request: KaraokePlayRequest) {
            Log.d(TAG, "onPrePlay mid: ${request.mid} ${request.songInfoObject.songName}")
            KaraokeConsole.shouldShowPicUrl.postValue("")
        }

        override fun onPrepareResourceBufferingChange(
            request: KaraokePlayRequest,
            bufferPercent: Int,
        ) {
            Log.d(TAG, "onPrepareResourceBufferingChange:$bufferPercent")
        }

        override fun onVideoBufferPercentChange(request: KaraokePlayRequest, percent: Int) {
            Log.d(TAG, "onVideoBufferPercentChange: $percent")
        }

        /**
         * 所有播放资源都已完成的回调。
         * <p>
         * 为适配多种弱网环境，同一时间sdk只允许下载一首歌曲的资源，当preload()再次被调用时，会取消之前的下载。
         * 所以一定要在收到这个回调后，再调用{@link com.tme.ktv.player.preload.PlayPreLoader#preload}进行下一首歌曲的预加载，
         * 否则会导致当前播放歌曲的下载被取消，从而播放器因资源不足而报播放异常错误。
         */
        override fun onAllResourceDownloadFinished(request: KaraokePlayRequest?) {
            //进行下一首歌曲的预加载
        }

        override fun onPlayPrepare(request: KaraokePlayRequest) {
            super.onPlayPrepare(request)
            Log.d(TAG, "onPlayPrepare $request")
        }

        override fun onPlayError(request: KaraokePlayRequest?, throwable: Throwable?) {
            Log.e(TAG, "onPlayError message: ${throwable?.message}")
            mainLaunch {
                KaraokeConsole.currPlayError.postValue(-100)
                mainViewModel?.lyricStrLiveData?.postValue("")
            }
            KaraokeConsole.playState.postValue(mPlayer.playState)
        }

        //有的定制厂商的资源无专辑图片，可使用此默认图片地址
        private val DEFAULT_IMAGE_URL =
            "https://y.gtimg.cn/music/photo_new/T029R960x540M000001iBG0q3mUVzD.jpg"

        /**
         * 视频播放失败或无视频，需要加载图片，此处会回调歌曲图片url
         */
        override fun onShowPicture(request: KaraokePlayRequest, url: String?, isError: Boolean) {
            Log.e(TAG, "onShowPicture: $url, isError: $isError")
            synchronized(this) {
                var finalUrl = url
                if (TextUtils.isEmpty(url)) {
                    finalUrl = DEFAULT_IMAGE_URL
                    if (KaraokeConsole.shouldShowPicUrl.value != DEFAULT_IMAGE_URL) {
                        Log.e(TAG, "onShowPicture 无专辑图片地址，使用默认图片")
                    }
                }
                KaraokeConsole.shouldShowPicUrl.postValue(finalUrl)
            }
        }
    }

    /**
     * 实现 BrightnessGestureCallback 接口
     * 手势结束时设置实际亮度
     */
    override fun onBrightnessGestureEnd() {
        mViewModel?.screenData?.value?.let { brightness ->
            var mBrightness = brightness
            Log.d(TAG, "Processing brightness: $mBrightness")
            // 移除 brightness > 0 的限制，允许设置任何亮度值（包括0.0）
            // 因为即使是很低的亮度值（如0.1）也应该被设置
            if (mBrightness >= 0) {
                try {
                    if (mBrightness == 0f) {
                        Log.d(TAG, "onBrightnessGestureEnd: brightness is 0, let it = 0.1")
                        mBrightness = 0.1f
                    }
                    Log.d(TAG, "onBrightnessGestureEnd: setting brightness=$mBrightness")
                    GlobalConfigHelper.setAppBrightness(mBrightness, context)
                } catch (e: Exception) {
                    // 异常处理：记录错误但不影响UI操作
                    Log.e(TAG, "onBrightnessGestureEnd: failed to set brightness", e)
                }
            } else {
                Log.d(TAG, "onBrightnessGestureEnd: brightness < 0, not setting")
            }
        } ?: run {
            Log.w(TAG, "onBrightnessGestureEnd: screenData value is null")
        }
    }
}