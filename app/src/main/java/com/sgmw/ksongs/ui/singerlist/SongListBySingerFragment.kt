package com.sgmw.ksongs.ui.singerlist

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R

import com.sgmw.ksongs.databinding.FragmentSongListBySingerBinding
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.showToast

import com.sgmw.ksongs.viewmodel.singerlist.SongListBySingerViewModel
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager


class SongListBySingerFragment : BaseFrameFragment<FragmentSongListBySingerBinding, SongListBySingerViewModel>() {

    private val TAG = "SongListBySingerFragment"

    companion object {
        private const val KEY_CARD_NAME = "card_name"

        /***
         * @param singerId 歌手id
         * @param singerName 歌手名
         * @param cardName 进入歌手详情页面的路径
         */
        fun createBundle(singerId: String, singerName: String, cardName: String = ""): Bundle {
            return Bundle().apply {
                putString("singerId", singerId)
                putString("singerName", singerName)
                putString(KEY_CARD_NAME, cardName)
            }
        }
    }


    private val mSingerId: String by lazy {
        arguments?.getString("singerId").toString()
    }

    private val mAdapter: RankListAdapter by lazy {
        RankListAdapter()
    }

    override fun FragmentSongListBySingerBinding.initView() {

        mBinding?.let { binding ->
            binding.ivBack.setOnSingleClickListener {
                findNavController().popBackStack()
            }

            val singerName = arguments?.getString("singerName")
            val cardName = arguments?.getString(KEY_CARD_NAME, "") ?: ""
            mAdapter.setCardName(cardName)
            binding.tvTitle.text = singerName
            val layoutManagerSong =
                AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            binding.recyclerview.layoutManager = layoutManagerSong
            binding.recyclerview.adapter = mAdapter
            mAdapter.setOnItemClickListener { adapter, v, position ->
                KaraokePlayerManager.playSong(this@SongListBySingerFragment, mAdapter.getItem(position), cardName)
            }
            binding.refreshLayout.setOnLoadMoreListener {
                mViewModel?.getSongsListBySingerId(mSingerId, Operation.LoadMore)
            }

        }

    }

    private fun setErrorEntryListener() {
        mBinding?.stateLayout?.setErrorRetryClickListener {
            mBinding?.stateLayout?.showLoading()
            mViewModel?.getSongsListBySingerId(mSingerId, Operation.NewData)
        }
    }

    /**
     * 设置空数据监听
     */
    private fun setEmptyDataListener() {
        mBinding?.stateLayout?.setEmptyClickListener {
            NavigationUtils.navigateSafely(findNavController(), R.id.action_song_list_by_singer_to_search)
        }
    }

    override fun initRequestData() {
        mBinding?.stateLayout?.showLoading()
        mViewModel?.getSongsListBySingerId(mSingerId, Operation.NewData)
    }

    override fun initObserve() {
        mViewModel?.let { viewModel ->
            mBinding?.let { binding ->
                viewModel.mSongsListBySingerBean.observe(viewLifecycleOwner) { result ->
                    result.onSuccess { value, operation ->
                        Log.d(TAG, "mSongsListBySingerBean.observe $value")
                        if (value == null) {//没有网络
                            binding.stateLayout.showEmpty()
                            setEmptyDataListener()
                        } else {
                            val songs = value.songs
                            when (operation) {
                                Operation.LoadMore -> {
                                    if (songs.isNullOrEmpty()) {
                                        if (!value.has_more) {
                                            binding.refreshLayout.finishLoadMoreWithNoMoreData()
                                            // 没有更多数据时，禁用 hasMoreData 标志，允许向上滑动回弹效果
                                            binding.recyclerview.setHasMoreData(false)
                                        }
                                    } else {
                                        mAdapter.addData(songs)
                                        // 根据是否还有更多数据来决定SmartRefreshLayout的状态
                                        if (value.has_more) {
                                            binding.refreshLayout.finishLoadMore()
                                            // 还有更多数据，设置 hasMoreData 为 true，不启用向上滑动回弹
                                            binding.recyclerview.setHasMoreData(true)
                                        } else {
                                            binding.refreshLayout.finishLoadMoreWithNoMoreData()
                                            // 没有更多数据时，启用向上滑动回弹效果
                                            binding.recyclerview.setHasMoreData(false)
                                        }
                                    }
                                }

                                Operation.NewData -> {
                                    if (songs.isNullOrEmpty()) {
                                        binding.stateLayout.showEmpty()
                                        binding.recyclerview.setHasMoreData(false)
                                    } else {
                                        mAdapter.setNewInstance(songs)
                                        binding.stateLayout.showContent()
                                        // 如果没有更多数据，需要设置 SmartRefreshLayout 的状态
                                        if (!value.has_more) {
                                            Log.d(TAG, "NewData: 设置 SmartRefreshLayout 没有更多数据")
                                            binding.refreshLayout.setNoMoreData(true)
                                            // 设置是否有更多数据
                                            binding.recyclerview.setHasMoreData(false)
                                        } else {
                                            Log.d(TAG, "NewData: 设置 SmartRefreshLayout 还有更多数据")
                                            binding.refreshLayout.setNoMoreData(false)
                                            // 设置是否有更多数据
                                            binding.recyclerview.setHasMoreData(true)
                                        }
                                    }

                                }

                                Operation.UpdateStatus -> {
                                    mAdapter.setList(songs)
                                }

                                else -> {}
                            }

                        }

                    }.onFailure { resultCode, operation ->
                        when (operation) {
                            Operation.NewData -> {
                                binding.stateLayout?.showError()
                                setErrorEntryListener()
                            }

                            Operation.LoadMore -> {
                                binding.refreshLayout.finishLoadMore(false)
                                showToast(R.string.load_more_failed)
                            }

                            else -> {}
                        }
                    }
                }
            }

            KaraokeConsole.playState.observe(this) {
                mAdapter.notifyDataSetChanged()
            }

            viewModel.collectSongChangeLiveData.observe(viewLifecycleOwner) {
                if (!mAdapter.data.isNullOrEmpty()) {
                    viewModel.updateCollectStatus(mAdapter.data)
                }
            }

            viewModel.demandSongInfo.observe(viewLifecycleOwner) {
                if (!mAdapter.data.isNullOrEmpty()) {
                    viewModel.updateDemandStatus(mAdapter.data)
                }
            }
        }
    }

}